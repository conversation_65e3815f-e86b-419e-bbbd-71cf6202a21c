package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.BaseTenantModeListener;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

/**
 * AI搜索事件实体
 */
@Data
@Entity
@QueryEntity
@Table(name = "ai_search_event")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class, BaseTenantModeListener.class})
public class AiSearchEventModel extends BaseTenantModel {
    
    /**
     * 关联的消息ID
     */
    @Column(name = "message_id", nullable = false)
    private Long messageId;
    
    /**
     * 会话ID
     */
    @Column(name = "session_id", nullable = false)
    private Long sessionId;
    
    /**
     * 事件类型（如：message、search_results、error、close等）
     */
    @Column(name = "event_type", nullable = false, length = 50)
    private String eventType;
    
    /**
     * 事件内容
     */
    @Lob
    @Column(name = "event_content")
    private String eventContent;
    
    /**
     * 事件序号（在同一消息中的顺序）
     */
    @Column(name = "sequence_order", nullable = false)
    private Integer sequenceOrder;
} 