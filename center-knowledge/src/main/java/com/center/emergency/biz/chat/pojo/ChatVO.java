package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class ChatVO {

    private Long questionId;

    private Long answerId;

    private String question;

    private Long sessionId;

    private Long robotId;

    private ChatTypeEnum chatType;

    /**
     * MCP服务ID列表，用于MCP策略指定特定的MCP服务
     */
    private List<Long> mcpIds;
}
