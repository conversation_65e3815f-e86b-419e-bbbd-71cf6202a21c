package com.center.emergency.biz.aisearch.service.impl;

import com.center.emergency.biz.aisearch.common.AiSearchUtils;
import com.center.emergency.biz.aisearch.common.listener.AiSearchSSEListener;
import com.center.emergency.biz.aisearch.common.parser.AiSearchSseParser;
import com.center.emergency.biz.aisearch.common.parser.AiSearchStreamParser;
import com.center.emergency.biz.aisearch.common.persistence.AiSearchParsedEvent;
import com.center.emergency.biz.aisearch.persistence.*;
import com.center.emergency.biz.aisearch.pojo.SearchMode;
import com.center.emergency.biz.aisearch.pojo.SearchRequest;
import com.center.emergency.biz.aisearch.service.AiSearchService;
import com.center.emergency.biz.aisearch.service.AiSearchChatUtils;
import com.center.emergency.biz.knowledgebase.service.KnowledgeBaseService;
import com.center.emergency.biz.knowledgebase.pojo.SearchResult;
import com.center.emergency.biz.knowledgebase.pojo.KnowledgeBaseDetail;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.web.pojo.CommonResult;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.transaction.annotation.Transactional;
import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageReq;
import com.center.emergency.biz.aisearch.pojo.AiSearchSessionPageResp;
import com.center.framework.web.pojo.PageResult;
import com.center.emergency.biz.aisearch.persistence.QAiSearchSessionModel;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.querydsl.core.BooleanBuilder;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

/**
 * AI搜索服务实现类 - 使用Chat模块的事件处理架构
 */
@Service
public class AiSearchServiceImpl implements AiSearchService {
    
    private static final Logger log = LoggerFactory.getLogger(AiSearchServiceImpl.class);
    
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    
    @Autowired
    private AiSearchSessionRepository sessionRepository;
    
    @Autowired
    private AiSearchMessageRepository messageRepository;
    
    @Autowired
    private AiSearchEventRepository eventRepository;
    
    @Autowired
    private Executor taskExecutor;
    
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Autowired
    private AiSearchChatUtils aiSearchChatUtils;
    
    /**
     * 全网搜索算法接口URL
     */
    @Value("${ai.search.global.url}")
    private String globalSearchUrl;
    
    /**
     * 企业内部搜索算法接口URL
     */
    @Value("${ai.search.internal.url}")
    private String internalSearchUrl;
    
    @Override
    public SseEmitter search(SearchRequest request) {
        log.info("开始执行AI搜索: question={}, searchMode={}", request.getQuestion(), request.getSearchMode());
        
        SseEmitter emitter = new SseEmitter(AiSearchUtils.getSseTimeoutMs());
        
        try {
            // 创建或获取会话
            Long sessionId = createOrGetSession(request);
            request.setSessionId(sessionId);
            
            // 创建用户消息记录，让框架自动生成ID
            Long userMessageId = createUserMessage(request);
            
            // 构建历史记录（使用用户消息ID作为当前消息ID）- 新格式
            List<Map<String, Object>> chatHistory = buildSearchHistory(sessionId, userMessageId);

            // 根据搜索模式构建请求参数
            HashMap<String, Object> param;
            String url;

            SearchMode mode = SearchMode.fromCode(request.getSearchMode());
            if (mode == SearchMode.GLOBAL) {
                // 全网搜索：直接使用新格式的历史记录
                param = AiSearchUtils.buildGlobalSearchParam(request, chatHistory, jpaQueryFactory);
                url = globalSearchUrl;
            } else {
                // 企业内部搜索：使用知识库和文件ID
                Set<String> kbIds = new HashSet<>();
                List<String> fileIds = new ArrayList<>();
                List<Map<String, String>> tagsList = new ArrayList<>();
                
                // 获取用户有权限的知识库
                List<Long> accessibleKbIds = getUserAccessibleKnowledgeBases();
                
                // 如果提供了知识库ID，验证权限并优先使用
                if (request.getKbIds() != null && !request.getKbIds().isEmpty()) {
                    for (Long kbId : request.getKbIds()) {
                        // 检查权限：只处理用户有权限的知识库
                        if (accessibleKbIds.contains(kbId)) {
                            aiSearchChatUtils.buildFromKnowledgeBase(kbId, kbIds, fileIds, jpaQueryFactory);
                            tagsList.addAll(aiSearchChatUtils.getTagsFromKnowledgeBase(kbId, jpaQueryFactory));
                        } else {
                            log.warn("用户无权限访问知识库: kbId={}", kbId);
                        }
                    }
                } else {
                    // 如果没有指定知识库，自动使用所有有权限的知识库
                    log.info("未指定知识库，自动使用所有有权限的知识库: count={}", accessibleKbIds.size());
                    for (Long kbId : accessibleKbIds) {
                        aiSearchChatUtils.buildFromKnowledgeBase(kbId, kbIds, fileIds, jpaQueryFactory);
                        tagsList.addAll(aiSearchChatUtils.getTagsFromKnowledgeBase(kbId, jpaQueryFactory));
                    }
                    log.info("自动构建的搜索范围: kbIds={}, fileIds.size={}, tags.size={}", 
                            kbIds, fileIds.size(), tagsList.size());
                }
                
                // 构建模型组配置
                List<Map<String, Object>> modelGroup = AiSearchUtils.buildDefaultModelGroup();
                
                param = AiSearchUtils.buildInternalSearchParam(request, chatHistory, kbIds, fileIds, tagsList, modelGroup);
                url = internalSearchUrl;
            }
            
            // === 使用Chat模块的事件处理架构 ===
            
            // 发送header事件 - 与chat模块保持一致
            try {
                Map<String, Object> headerData = new HashMap<>();
                headerData.put("sessionId", sessionId);
                headerData.put("userMessageId", userMessageId);
                headerData.put("question", request.getQuestion());
                headerData.put("searchMode", request.getSearchMode());
                emitter.send(SseEmitter.event().name("header").data(CommonResult.success(headerData)));
            } catch (IOException e) {
                log.error("发送header事件失败", e);
                emitter.completeWithError(e);
                return emitter;
            }
            
            // 1. 创建CountDownLatch (这个latch会被共享)
            CountDownLatch latch = new CountDownLatch(1);
            
            // 2. 创建解析器，传入共享的latch
            AiSearchSseParser parser = new AiSearchSseParser(
                emitter, 
                this, 
                false, 
                request, 
                new StringBuilder(),
                latch // 传入共享的latch
            );
            
            // 3. 异步执行搜索请求
            log.info("准备发送AI搜索请求:");
            log.info("  URL: {}", url);
            log.info("  搜索模式: {}", mode);
            log.info("  完整请求参数: {}", param);
            
            AiSearchUtils.searchRunAsync(
                emitter, 
                request, 
                url, 
                param, 
                taskExecutor, 
                parser, 
                (AiSearchUtils.ListenerFactory) (parserObj, em, req) -> new AiSearchSSEListener(
                    (AiSearchStreamParser) parserObj,
                    latch, // 使用共享的latch而不是创建新的
                    em,
                    this,
                    req,
                    sessionId,
                    userMessageId,
                    new StringBuilder()
                )
            );
            
            log.info("AI搜索请求已提交，sessionId={}, userMessageId={}", 
                    sessionId, userMessageId);
            
        } catch (Exception e) {
            log.error("AI搜索启动失败: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        }
        
        return emitter;
    }
    
    @Override
    public List<Map<String, Object>> getSessionHistory(Long sessionId) {
        log.info("获取会话历史: sessionId={}", sessionId);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 验证会话权限
        if (!sessionRepository.existsByIdAndTenantIdAndCreatorId(sessionId, tenantId, userId)) {
            throw new RuntimeException("会话不存在或无权限访问");
        }
        
        // 查询消息列表
        List<AiSearchMessageModel> messages = messageRepository.findBySessionIdAndTenantIdOrderByCreateTimeAsc(sessionId, tenantId);
        
        List<Map<String, Object>> history = new ArrayList<>();
        for (AiSearchMessageModel message : messages) {
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("id", message.getId());
            messageMap.put("role", message.getRole());
            messageMap.put("content", message.getContent());
            messageMap.put("createTime", message.getCreateTime());
            messageMap.put("thumbsUp", message.getThumbsUp());
            
            // 如果是用户消息，添加搜索参数
            if ("user".equals(message.getRole())) {
                messageMap.put("searchMode", message.getSearchMode());
                
                // 添加搜索模式描述
                if (message.getSearchMode() != null) {
                    try {
                        SearchMode mode = SearchMode.fromCode(message.getSearchMode());
                        messageMap.put("searchModeDesc", mode.getDesc());
                    } catch (IllegalArgumentException e) {
                        messageMap.put("searchModeDesc", "未知模式");
                    }
                }
                
                messageMap.put("searchEngine", message.getSearchEngine());
                messageMap.put("maxResults", message.getMaxResults());
                messageMap.put("kbIds", message.getKbIds());
                messageMap.put("fileIds", message.getFileIds());
            }
            
            history.add(messageMap);
        }
        
        return history;
    }
    
    @Override
    @Transactional
    public void deleteSession(Long sessionId) {
        log.info("删除会话: sessionId={}", sessionId);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 验证会话权限
        if (!sessionRepository.existsByIdAndTenantIdAndCreatorId(sessionId, tenantId, userId)) {
            throw new RuntimeException("会话不存在或无权限访问");
        }
        
        // 删除所有事件记录
        eventRepository.deleteBySessionIdAndTenantId(sessionId, tenantId);
        
        // 删除所有消息记录
        messageRepository.deleteBySessionIdAndTenantId(sessionId, tenantId);
        
        // 删除会话记录
        sessionRepository.deleteById(sessionId);
        
        log.info("会话删除完成: sessionId={}", sessionId);
    }
    
    @Override
    public List<Map<String, Object>> getUserSessions() {
        log.info("获取用户会话列表: userId={}", LoginContextHolder.getLoginUserId());
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 查询用户的会话列表
        List<AiSearchSessionModel> sessions = sessionRepository.findByTenantIdAndCreatorIdOrderByCreateTimeDesc(tenantId, userId);
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (AiSearchSessionModel session : sessions) {
            Map<String, Object> sessionMap = new HashMap<>();
            sessionMap.put("id", session.getId());
            sessionMap.put("title", session.getTitle());
            sessionMap.put("searchMode", session.getSearchMode());
            sessionMap.put("searchEngine", session.getSearchEngine());
            sessionMap.put("createTime", session.getCreateTime());
            sessionMap.put("updateTime", session.getUpdateTime());
            
            // 统计消息数量
            long messageCount = messageRepository.countBySessionIdAndTenantId(session.getId(), tenantId);
            sessionMap.put("messageCount", messageCount);
            
            result.add(sessionMap);
        }
        
        return result;
    }
    
    @Override
    public List<Map<String, Object>> getUserSessionsByMode(Integer searchMode) {
        log.info("根据搜索模式获取用户会话列表: userId={}, searchMode={}", LoginContextHolder.getLoginUserId(), searchMode);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 查询指定搜索模式的会话列表
        List<AiSearchSessionModel> sessions = sessionRepository.findByTenantIdAndCreatorIdAndSearchModeOrderByCreateTimeDesc(tenantId, userId, searchMode);
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (AiSearchSessionModel session : sessions) {
            Map<String, Object> sessionMap = new HashMap<>();
            sessionMap.put("id", session.getId());
            sessionMap.put("title", session.getTitle());
            sessionMap.put("searchMode", session.getSearchMode());
            sessionMap.put("searchEngine", session.getSearchEngine());
            sessionMap.put("createTime", session.getCreateTime());
            sessionMap.put("updateTime", session.getUpdateTime());
            
            // 统计消息数量
            long messageCount = messageRepository.countBySessionIdAndTenantId(session.getId(), tenantId);
            sessionMap.put("messageCount", messageCount);
            
            result.add(sessionMap);
        }
        
        return result;
    }
    
    @Override
    public PageResult<AiSearchSessionPageResp> pageUserSessions(AiSearchSessionPageReq request) {
        log.info("分页查询用户会话列表: userId={}, 分页参数={}:{}, 搜索条件: keyword={}, searchMode={}", 
                LoginContextHolder.getLoginUserId(), request.getPageNo(), request.getPageSize(),
                request.getKeyword(), request.getSearchMode());
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 构建分页条件
        Pageable pageable = PageRequest.of(request.getPageNo() - 1, request.getPageSize());
        
        // 构建查询条件
        QAiSearchSessionModel qSession = QAiSearchSessionModel.aiSearchSessionModel;
        QUserModel qUser = QUserModel.userModel;
        BooleanBuilder builder = new BooleanBuilder();
        
        // 基础条件：租户ID和创建者ID
        builder.and(qSession.tenantId.eq(tenantId));
        builder.and(qSession.creatorId.eq(userId));
        
        // 关键词模糊搜索（会话标题）
        if (StringUtils.hasText(request.getKeyword())) {
            builder.and(qSession.title.containsIgnoreCase(request.getKeyword()));
        }
        
        // 搜索模式筛选
        if (request.getSearchMode() != null) {
            builder.and(qSession.searchMode.eq(request.getSearchMode()));
        }
        
        // 搜索引擎类型筛选
        if (StringUtils.hasText(request.getSearchEngine())) {
            builder.and(qSession.searchEngine.eq(request.getSearchEngine()));
        }
        
        // 执行分页查询
        JPQLQuery<AiSearchSessionPageResp> jpqlQuery = jpaQueryFactory.select(
                Projections.bean(
                        AiSearchSessionPageResp.class,
                        qSession.id,
                        qSession.title,
                        qSession.searchMode,
                        qSession.searchEngine,
                        qSession.createTime,
                        qSession.updateTime,
                        qUser.displayName.as("creatorName")
                ))
                .from(qSession)
                .leftJoin(qUser).on(qSession.creatorId.eq(qUser.id))
                .where(builder)
                .orderBy(qSession.updateTime.desc()) // 按更新时间倒序
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());
        
        Long total = jpqlQuery.fetchCount();
        List<AiSearchSessionPageResp> resultList = jpqlQuery.fetch();
        
        // 补充数据：枚举描述和消息统计
        for (AiSearchSessionPageResp session : resultList) {
            // 添加搜索模式描述
            if (session.getSearchMode() != null) {
                try {
                    SearchMode mode = SearchMode.fromCode(session.getSearchMode());
                    session.setSearchModeDesc(mode.getDesc());
                } catch (IllegalArgumentException e) {
                    session.setSearchModeDesc("未知模式");
                }
            }
            
            // 统计消息数量
            long messageCount = messageRepository.countBySessionIdAndTenantId(session.getId(), tenantId);
            session.setMessageCount(messageCount);
        }
        
        log.info("分页查询用户会话列表完成: 总数={}, 当前页数据量={}", total, resultList.size());
        return PageResult.of(resultList, total);
    }
    
    @Override
    public Map<String, Object> getMessageDetail(Long messageId) {
        log.info("获取消息详情: messageId={}", messageId);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        
        // 查询消息信息
        Optional<AiSearchMessageModel> messageOpt = messageRepository.findById(messageId);
        if (!messageOpt.isPresent()) {
            throw new RuntimeException("消息不存在");
        }
        
        AiSearchMessageModel message = messageOpt.get();
        if (!tenantId.equals(message.getTenantId())) {
            throw new RuntimeException("无权限访问该消息");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", message.getId());
        result.put("sessionId", message.getSessionId());
        result.put("role", message.getRole());
        result.put("content", message.getContent());
        result.put("searchMode", message.getSearchMode());
        
        // 添加搜索模式描述
        if (message.getSearchMode() != null) {
            try {
                SearchMode mode = SearchMode.fromCode(message.getSearchMode());
                result.put("searchModeDesc", mode.getDesc());
            } catch (IllegalArgumentException e) {
                result.put("searchModeDesc", "未知模式");
            }
        }
        
        result.put("searchEngine", message.getSearchEngine());
        result.put("maxResults", message.getMaxResults());
        result.put("kbIds", message.getKbIds());
        result.put("fileIds", message.getFileIds());
        result.put("thumbsUp", message.getThumbsUp());
        result.put("createTime", message.getCreateTime());
        result.put("updateTime", message.getUpdateTime());
        
        // 查询关联的事件
        List<AiSearchEventModel> events = eventRepository.findByMessageIdAndTenantIdOrderBySequenceOrderAsc(messageId, tenantId);
        List<Map<String, Object>> eventList = new ArrayList<>();
        for (AiSearchEventModel event : events) {
            Map<String, Object> eventMap = new HashMap<>();
            eventMap.put("id", event.getId());
            eventMap.put("eventType", event.getEventType());
            eventMap.put("eventContent", event.getEventContent());
            eventMap.put("sequenceOrder", event.getSequenceOrder());
            eventMap.put("createTime", event.getCreateTime());
            eventList.add(eventMap);
        }
        result.put("events", eventList);
        
        return result;
    }
    
    @Override
    @Transactional
    public void updateMessageThumbsUp(Long messageId, Integer thumbsUp) {
        log.info("更新消息点赞状态: messageId={}, thumbsUp={}", messageId, thumbsUp);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        
        Optional<AiSearchMessageModel> messageOpt = messageRepository.findById(messageId);
        if (!messageOpt.isPresent()) {
            throw new RuntimeException("消息不存在");
        }
        
        AiSearchMessageModel message = messageOpt.get();
        if (!tenantId.equals(message.getTenantId())) {
            throw new RuntimeException("无权限操作该消息");
        }
        
        // 只允许对assistant角色的消息进行点赞
        if (!"assistant".equals(message.getRole())) {
            throw new RuntimeException("只能对助手回复进行点赞操作");
        }
        
        message.setThumbsUp(thumbsUp);
        messageRepository.save(message);
        
        log.info("消息点赞状态更新完成: messageId={}, thumbsUp={}", messageId, thumbsUp);
    }
    
    @Override
    public Map<String, Object> getSessionStats(Long sessionId) {
        log.info("获取会话统计信息: sessionId={}", sessionId);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 验证会话权限
        if (!sessionRepository.existsByIdAndTenantIdAndCreatorId(sessionId, tenantId, userId)) {
            throw new RuntimeException("会话不存在或无权限访问");
        }
        
        Map<String, Object> stats = new HashMap<>();
        
        // 消息统计
        long totalMessages = messageRepository.countBySessionIdAndTenantId(sessionId, tenantId);
        List<AiSearchMessageModel> userMessages = messageRepository.findBySessionIdAndTenantIdAndRoleOrderByCreateTimeAsc(sessionId, tenantId, "user");
        List<AiSearchMessageModel> assistantMessages = messageRepository.findBySessionIdAndTenantIdAndRoleOrderByCreateTimeAsc(sessionId, tenantId, "assistant");
        
        stats.put("totalMessages", totalMessages);
        stats.put("userMessageCount", userMessages.size());
        stats.put("assistantMessageCount", assistantMessages.size());
        
        // 事件统计
        long totalEvents = eventRepository.countBySessionIdAndTenantId(sessionId, tenantId);
        stats.put("totalEvents", totalEvents);
        
        // 点赞统计
        long thumbsUpCount = assistantMessages.stream()
                .filter(m -> m.getThumbsUp() != null && m.getThumbsUp() == 1)
                .count();
        long thumbsDownCount = assistantMessages.stream()
                .filter(m -> m.getThumbsUp() != null && m.getThumbsUp() == -1)
                .count();
        stats.put("thumbsUpCount", thumbsUpCount);
        stats.put("thumbsDownCount", thumbsDownCount);
        
        // 搜索模式统计
        Map<Integer, Long> searchModeStats = userMessages.stream()
                .filter(m -> m.getSearchMode() != null)
                .collect(java.util.stream.Collectors.groupingBy(
                        AiSearchMessageModel::getSearchMode,
                        java.util.stream.Collectors.counting()));
        stats.put("searchModeStats", searchModeStats);
        
        return stats;
    }
    
    @Override
    @Transactional
    public void updateSessionTitle(Long sessionId, String title) {
        log.info("更新会话标题: sessionId={}, title={}", sessionId, title);
        
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();
        
        // 验证会话权限
        if (!sessionRepository.existsByIdAndTenantIdAndCreatorId(sessionId, tenantId, userId)) {
            throw new RuntimeException("会话不存在或无权限访问");
        }
        
        Optional<AiSearchSessionModel> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            AiSearchSessionModel session = sessionOpt.get();
            session.setTitle(title);
            sessionRepository.save(session);
            
            log.info("会话标题更新完成: sessionId={}, newTitle={}", sessionId, title);
        }
    }
    
    /**
     * 创建或获取会话
     */
    private Long createOrGetSession(SearchRequest request) {
        if (request.getSessionId() != null) {
            log.info("使用现有会话: sessionId={}", request.getSessionId());
            return request.getSessionId();
        }

        try {
            // 获取当前用户上下文信息用于调试
            Long tenantId = LoginContextHolder.getLoginUserTenantId();
            Long userId = LoginContextHolder.getLoginUserId();
            log.info("创建AI搜索会话 - 当前用户上下文: tenantId={}, userId={}", tenantId, userId);

            // 创建新会话，让框架自动生成ID
            AiSearchSessionModel session = new AiSearchSessionModel();
            session.setTitle(AiSearchUtils.generateSessionTitle(request.getQuestion(), AiSearchUtils.getDefaultMaxTitleLength()));
            session.setSearchMode(request.getSearchMode());
            session.setSearchEngine(request.getSearchEngine());

            session = sessionRepository.save(session);

            log.info("创建新会话成功: sessionId={}, title={}, tenantId={}",
                    session.getId(), session.getTitle(), session.getTenantId());
            return session.getId();
        } catch (Exception e) {
            log.error("创建AI搜索会话失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建AI搜索会话失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建用户消息记录
     */
    private Long createUserMessage(SearchRequest request) {
        AiSearchMessageModel message = new AiSearchMessageModel();
        message.setSessionId(request.getSessionId());
        message.setRole("user");
        message.setContent(request.getQuestion());
        message.setSearchMode(request.getSearchMode());
        message.setSearchEngine(request.getSearchEngine());
        message.setMaxResults(request.getMaxResults());
        
        // 设置JSON字段
        if (request.getKbIds() != null && !request.getKbIds().isEmpty()) {
            message.setKbIds(request.getKbIds().toString());
        }
        if (request.getFileIds() != null && !request.getFileIds().isEmpty()) {
            message.setFileIds(request.getFileIds().toString());
        }
        
        messageRepository.save(message);
        
        log.info("创建用户消息: messageId={}, sessionId={}, content={}", 
                message.getId(), request.getSessionId(), request.getQuestion());
        return message.getId();
    }
    
    /**
     * 构建搜索历史记录（新格式）
     */
    private List<Map<String, Object>> buildSearchHistory(Long sessionId, Long currentMessageId) {
        log.info("构建搜索历史: sessionId={}, currentMessageId={}", sessionId, currentMessageId);

        // 使用aisearch模块独立的历史记录构建逻辑，返回新格式
        List<Map<String, Object>> history = aiSearchChatUtils.getHistory(sessionId, currentMessageId, false, jpaQueryFactory);

        log.debug("搜索历史记录条数: {}", history.size());
        return history;
    }
    
    /**
     * 保存搜索问题和答案及其所有事件（参考chat模块的saveQuestionAndAnswerWithEvents方法）
     * 使用新的事件处理架构
     */
    @Transactional
    public void saveSearchQuestionAndAnswerWithEvents(Long sessionId, Long userMessageId, List<AiSearchParsedEvent> parsedEvents) {
        log.info("保存搜索问题和答案及事件: sessionId={}, userMessageId={}, 解析事件总数={}", 
                sessionId, userMessageId, parsedEvents.size());
        
        try {
            // 从解析事件中聚合主要回答内容
            StringBuilder messageBuilder = new StringBuilder();
            List<AiSearchEventModel> eventModels = new ArrayList<>();
            
            for (int i = 0; i < parsedEvents.size(); i++) {
                AiSearchParsedEvent parsedEvent = parsedEvents.get(i);
                
                // 转换为数据库事件模型
                AiSearchEventModel eventModel = new AiSearchEventModel();
                eventModel.setSessionId(sessionId);
                eventModel.setEventType(parsedEvent.getEventType().name().toLowerCase());
                eventModel.setEventContent(parsedEvent.getContent());
                eventModel.setSequenceOrder(i + 1);
                eventModels.add(eventModel);
                
                // 如果是MESSAGE类型，累积到主要回答内容中
                if (parsedEvent.getEventType().name().equals("MESSAGE") && parsedEvent.getContent() != null) {
                    messageBuilder.append(parsedEvent.getContent());
                }
            }
            
            String finalContent = messageBuilder.toString();
            log.info("聚合后的搜索回答内容长度: {}", finalContent.length());
            
            // 创建助手消息记录
            AiSearchMessageModel assistantMessage = new AiSearchMessageModel();
            assistantMessage.setSessionId(sessionId);
            assistantMessage.setRole("assistant");
            assistantMessage.setContent(finalContent);
            
            assistantMessage = messageRepository.save(assistantMessage);
            
            // 保存所有事件详情，关联到助手消息
            for (AiSearchEventModel eventModel : eventModels) {
                eventModel.setMessageId(assistantMessage.getId());
                eventRepository.save(eventModel);
            }
            
            log.info("搜索问题和答案及事件保存完成: assistantMessageId={}, 内容长度={}, 事件数量={}", 
                    assistantMessage.getId(), finalContent.length(), eventModels.size());
            
        } catch (Exception e) {
            log.error("保存搜索数据失败: sessionId={}, userMessageId={}, error={}", sessionId, userMessageId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存助手消息和所有事件（兼容旧版本，参考chat模块的saveQuestionAndAnswerWithEvents方法）
     */
    public void saveAssistantMessageWithEvents(Long sessionId, Long userMessageId, List<AiSearchEventModel> events) {
        log.info("保存助手消息和事件: sessionId={}, userMessageId={}, 事件总数={}", sessionId, userMessageId, events.size());
        
        // 从事件中聚合主要回答内容，支持多种格式
        StringBuilder messageBuilder = new StringBuilder();
        for (AiSearchEventModel event : events) {
            if (event.getEventContent() != null) {
                String eventType = event.getEventType();
                String content = event.getEventContent();
                
                // 处理不同类型的事件
                if ("message".equals(eventType) || eventType == null) {
                    // 尝试解析JSON格式的响应
                    try {
                        if (content.trim().startsWith("{")) {
                            // JSON格式，尝试提取response字段
                            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                            com.fasterxml.jackson.databind.JsonNode jsonNode = mapper.readTree(content);
                            
                            // 尝试多个可能的字段名
                            String responseText = null;
                            if (jsonNode.has("response")) {
                                responseText = jsonNode.get("response").asText();
                            } else if (jsonNode.has("content")) {
                                responseText = jsonNode.get("content").asText();
                            } else if (jsonNode.has("data")) {
                                responseText = jsonNode.get("data").asText();
                            }
                            
                            if (responseText != null && !responseText.trim().isEmpty()) {
                                messageBuilder.append(responseText);
                            }
                        } else {
                            // 直接是文本内容
                            messageBuilder.append(content);
                        }
                    } catch (Exception parseEx) {
                        log.debug("解析JSON失败，直接使用原始内容: {}", parseEx.getMessage());
                        messageBuilder.append(content);
                    }
                }
                // 其他类型的事件（如search_results, tool_call等）不加入主要消息内容
            }
        }
        
        String finalContent = messageBuilder.toString();
        log.info("聚合后的消息内容长度: {}", finalContent.length());
        
        // 创建助手消息记录，让框架自动生成ID
        AiSearchMessageModel assistantMessage = new AiSearchMessageModel();
        assistantMessage.setSessionId(sessionId);
        assistantMessage.setRole("assistant");
        assistantMessage.setContent(finalContent);
        
        assistantMessage = messageRepository.save(assistantMessage);
        
        // 保存所有事件详情，关联到助手消息
        for (int i = 0; i < events.size(); i++) {
            AiSearchEventModel event = events.get(i);
            event.setMessageId(assistantMessage.getId()); // 关联到助手消息
            event.setSessionId(sessionId);
            event.setSequenceOrder(i + 1);
            eventRepository.save(event);
        }
        
        log.info("助手消息和事件保存完成: assistantMessageId={}, 内容长度={}", assistantMessage.getId(), finalContent.length());
    }

    /**
     * 获取用户有权限的知识库
     */
    private List<Long> getUserAccessibleKnowledgeBases() {
        try {
            log.debug("正在获取用户有权限的知识库列表");
            
            // 使用知识库服务的搜索方法，传入空字符串获取所有有权限的知识库
            // 这个方法内部已经实现了权限控制逻辑
            SearchResult searchResult = knowledgeBaseService.searchKBases("");
            
            List<Long> accessibleKbIds = new ArrayList<>();
            
            if (searchResult != null && searchResult.getKnowledgeBases() != null) {
                for (KnowledgeBaseDetail kb : searchResult.getKnowledgeBases()) {
                    accessibleKbIds.add(kb.getId());
                }
                log.info("获取到用户有权限的知识库: count={}, ids={}", 
                        accessibleKbIds.size(), accessibleKbIds);
            } else {
                log.warn("未获取到任何知识库或搜索结果为空");
            }
            
            return accessibleKbIds;
            
        } catch (Exception e) {
            log.error("获取用户权限知识库时发生异常", e);
            // 如果发生异常，返回空列表，防止程序崩溃
            return new ArrayList<>();
        }
    }
} 