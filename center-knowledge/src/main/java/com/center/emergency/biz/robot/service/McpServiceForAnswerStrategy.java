package com.center.emergency.biz.robot.service;

import com.center.emergency.biz.chat.pojo.ChatVO;
import com.center.emergency.biz.chat.pojo.RobotModelDTO;
import com.center.emergency.biz.mcp.persistence.QMcpModel;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.modelgroup.service.ModelGroupService;
import com.center.emergency.common.enumeration.AnswerStrategyEnum;
import com.center.framework.common.pojo.IdAndValue;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.*;

@Component("MCP")
@Slf4j
public class McpServiceForAnswerStrategy extends AbstractAnswerStrategy implements AnswerStrategyService{

    @Resource
    private ModelGroupService modelGroupService;

    @Override
    public List<Long> listAnswerStrategyIds(Long robotId) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QMcpModel qMcpModel = QMcpModel.mcpModel;
        List<Long> result = queryFactory.select(qMcpModel.id)
                .from(qRobotKnowledgeModel).join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id)).
                where(qRobotKnowledgeModel.robotId.eq(robotId))
                .fetch();

        return result;
    }

    /**
     * MCP策略的参数构建 - 实现MCP特定的参数构建逻辑
     * 注意：MCP策略不使用kbIds和fileIds，这些参数会被忽略
     */
    @Override
    public HashMap<String, Object> buildChatParams(ChatVO chatVO, Set<String> kbIds, List<String> fileIds,
                                                   List<Map<String, String>> tags, Boolean isSimulate,
                                                   Long modelId, JPAQueryFactory jpaQueryFactory) {
        log.info("构建MCP策略对话参数: chatVO={}, modelId={}, isSimulate={} (忽略kbIds和fileIds)",
                chatVO.getQuestion(), modelId, isSimulate);

        // 1. 收集MCP服务配置（支持指定MCP列表）
        List<Map<String, Object>> dynamicServers = collectMcpServers(chatVO.getRobotId(), chatVO.getMcpIds(), jpaQueryFactory);

        // 2. 构建MCP策略基础参数
        HashMap<String, Object> param = buildMcpBaseParams(chatVO);

        // 3. 查询并添加模型配置
        if (modelId != null) {
            RobotModelDTO robotModelDTO = queryRobotModelConfig(chatVO.getRobotId(), modelId, jpaQueryFactory);
            // MCP策略只需要基础模型配置，不需要策略特有参数
            addBaseModelConfigParams(param, robotModelDTO);
        } else {
            // 使用系统默认模型配置
            Map<String, Object> defaultModelConfig = modelGroupService.getSystemDefaultModelConfig();
            param.putAll(defaultModelConfig);
        }

        // 4. 添加MCP特有参数
        param.put("dynamic_servers", dynamicServers);
        param.put("max_tool_rounds", 10); // 默认最大工具调用轮次

        log.info("MCP策略参数构建完成，dynamic_servers数量: {}", dynamicServers.size());
        return param;
    }

    /**
     * MCP策略的资源收集和参数构建 - 实现MCP特定的参数构建逻辑
     * 内部自动判断ChatVO中的mcpIds参数，决定是使用所有MCP还是指定的MCP
     */
    @Override
    public HashMap<String, Object> buildChatParamsWithResourceCollection(ChatVO chatVO, Boolean isSimulate,
                                                                         Long modelGroupId, JPAQueryFactory jpaQueryFactory) {
        log.info("MCP策略自主收集资源并构建参数: chatVO={}, modelGroupId={}, mcpIds={}, isSimulate={}",
                chatVO.getQuestion(), modelGroupId, chatVO.getMcpIds(), isSimulate);

        // 1. 收集MCP服务配置（内部判断是否指定了MCP列表）
        List<Map<String, Object>> dynamicServers = collectMcpServers(chatVO.getRobotId(), chatVO.getMcpIds(), jpaQueryFactory);

        // 2. 构建MCP策略基础参数
        HashMap<String, Object> param = buildMcpBaseParams(chatVO);

        // 3. 添加模型组配置
        addModelGroupParams(param, modelGroupId);

        // 4. 添加MCP特有参数
        param.put("dynamic_servers", dynamicServers);
        param.put("max_tool_rounds", 10); // 默认最大工具调用轮次

        if (CollectionUtils.isEmpty(chatVO.getMcpIds())) {
            log.info("MCP策略参数构建完成，使用所有关联的MCP，dynamic_servers数量: {}", dynamicServers.size());
        } else {
            log.info("MCP策略参数构建完成，使用指定的MCP {}，dynamic_servers数量: {}", chatVO.getMcpIds(), dynamicServers.size());
        }
        
        return param;
    }

    /**
     * 构建MCP策略的基础参数
     * 包含MCP策略特有的参数格式
     */
    private HashMap<String, Object> buildMcpBaseParams(ChatVO chatVO) {
        HashMap<String, Object> param = buildCommonBaseParams(chatVO);

        // MCP策略特有参数（使用query字段而不是question）
        param.put("query", chatVO.getQuestion()); // MCP策略使用query字段

        return param;
    }

    /**
     * 收集机器人关联的MCP服务配置 - 支持指定MCP列表
     * @param robotId 机器人ID
     * @param mcpIds 指定的MCP ID列表，为空时获取所有关联的MCP
     * @param jpaQueryFactory JPA查询工厂
     */
    private List<Map<String, Object>> collectMcpServers(Long robotId, List<Long> mcpIds, JPAQueryFactory jpaQueryFactory) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;
        QMcpModel qMcpModel = QMcpModel.mcpModel;

        BooleanBuilder whereBuilder = new BooleanBuilder();
        whereBuilder.and(qRobotKnowledgeModel.robotId.eq(robotId))
                .and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.MCP));

        // 如果指定了mcpIds，则需要验证这些MCP是否属于当前机器人
        if (!CollectionUtils.isEmpty(mcpIds)) {
            log.info("MCP策略收到指定MCP列表: {}", mcpIds);
            
            // 先验证指定的MCP是否都属于当前机器人
            List<Long> robotMcpIds = jpaQueryFactory
                    .select(qMcpModel.id)
                    .from(qRobotKnowledgeModel)
                    .join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id))
                    .where(qRobotKnowledgeModel.robotId.eq(robotId)
                            .and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.MCP)))
                    .fetch();

            // 过滤出确实属于该机器人的MCP
            List<Long> validMcpIds = new ArrayList<>();
            for (Long mcpId : mcpIds) {
                if (robotMcpIds.contains(mcpId)) {
                    validMcpIds.add(mcpId);
                } else {
                    log.warn("MCP {} 不属于机器人 {}，已跳过", mcpId, robotId);
                }
            }

            if (validMcpIds.isEmpty()) {
                log.warn("指定的MCP列表中没有属于机器人 {} 的MCP，返回空列表", robotId);
                return new ArrayList<>();
            }

            // 只查询有效的MCP
            whereBuilder.and(qMcpModel.id.in(validMcpIds));
            log.info("MCP策略验证后的有效MCP列表: {}", validMcpIds);
        }

        // 查询机器人关联的MCP服务
        List<com.querydsl.core.Tuple> mcpTuples = jpaQueryFactory
                .select(qMcpModel.id, qMcpModel.serverName, qMcpModel.serverUrl, qMcpModel.mcpDesc)
                .from(qRobotKnowledgeModel)
                .join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id))
                .where(whereBuilder)
                .fetch();

        // 转换为dynamic_servers格式
        List<Map<String, Object>> dynamicServers = new ArrayList<>();
        for (com.querydsl.core.Tuple tuple : mcpTuples) {
            Map<String, Object> server = new HashMap<>();
            server.put("server_key", tuple.get(qMcpModel.serverName));
            server.put("url", tuple.get(qMcpModel.serverUrl));
            server.put("description", tuple.get(qMcpModel.mcpDesc));
            server.put("make_default", false);
            dynamicServers.add(server);
            
            log.debug("添加MCP服务: id={}, serverName={}, url={}", 
                    tuple.get(qMcpModel.id), tuple.get(qMcpModel.serverName), tuple.get(qMcpModel.serverUrl));
        }

        return dynamicServers;
    }

}
