package com.center.emergency.biz.aisearch.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.BaseTenantModeListener;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.querydsl.core.annotations.QueryEntity;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

/**
 * AI搜索消息实体
 */
@Data
@Entity
@QueryEntity
@Table(name = "ai_search_message")
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class, BaseTenantModeListener.class})
public class AiSearchMessageModel extends BaseTenantModel {
    
    /**
     * 会话ID
     */
    @Column(name = "session_id", nullable = false)
    private Long sessionId;
    
    /**
     * 角色：user（用户）、assistant（助手）
     */
    @Column(name = "role", nullable = false, length = 20)
    private String role;
    
    /**
     * 对话内容
     */
    @Lob
    @Column(name = "content")
    private String content;
    
    /**
     * 搜索模式：1-全网搜索，2-企业内部搜索
     */
    @Column(name = "search_mode")
    private Integer searchMode;
    
    /**
     * 搜索引擎类型（全网搜索时使用）
     */
    @Column(name = "search_engine", length = 50)
    private String searchEngine;
    
    /**
     * 最大搜索结果数
     */
    @Column(name = "max_results")
    private Integer maxResults;
    
    /**
     * 知识库ID列表（JSON格式）
     */
    @Column(name = "kb_ids", length = 1000)
    private String kbIds;
    
    /**
     * 文件ID列表（JSON格式）
     */
    @Column(name = "file_ids", length = 1000)
    private String fileIds;
    
    /**
     * 点赞状态：0-无，1-赞，-1-踩
     */
    @Column(name = "thumbs_up")
    private Integer thumbsUp;
} 