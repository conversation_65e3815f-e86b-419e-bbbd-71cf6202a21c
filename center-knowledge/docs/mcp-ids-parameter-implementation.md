# MCP策略支持指定MCP服务实现文档

## 📋 需求描述

在`ChatWithRobotReq`中添加`mcpIds`参数，用于指定特定的MCP服务。实现逻辑：
- 如果`mcpIds`为空，正常拼接所有关联的MCP服务
- 如果`mcpIds`有值，验证这些MCP是否属于当前机器人，只拼接验证通过的MCP服务

## 🔧 修正后的实现方案

### 1. 请求参数扩展

**ChatWithRobotReq.java**
```java
@Schema(description = "MCP服务ID列表，用于指定特定的MCP服务。为空时使用机器人关联的所有MCP")
private List<Long> mcpIds;
```

### 2. 数据传输对象扩展

**ChatVO.java**
```java
/**
 * MCP服务ID列表，用于MCP策略指定特定的MCP服务
 */
private List<Long> mcpIds;
```

### 3. MCP策略服务实现

**McpServiceForAnswerStrategy.java**

#### 核心设计思路
- **完全由策略实现类负责**: 所有判断逻辑都在MCP策略实现类内部完成
- **统一方法入口**: 不增加额外的重载方法，保持接口简洁
- **内部判断**: 在`buildChatParamsWithResourceCollection`方法中直接从`ChatVO`获取`mcpIds`参数

#### 核心逻辑
```java
// 在buildChatParamsWithResourceCollection方法中
List<Map<String, Object>> dynamicServers = collectMcpServers(
    chatVO.getRobotId(), 
    chatVO.getMcpIds(),  // 直接从ChatVO获取mcpIds
    jpaQueryFactory
);
```

#### MCP权限验证
```java
private List<Map<String, Object>> collectMcpServers(Long robotId, List<Long> mcpIds, JPAQueryFactory jpaQueryFactory) {
    // 如果指定了mcpIds，验证这些MCP是否属于当前机器人
    if (!CollectionUtils.isEmpty(mcpIds)) {
        // 1. 查询机器人拥有的所有MCP
        List<Long> robotMcpIds = jpaQueryFactory
            .select(qMcpModel.id)
            .from(qRobotKnowledgeModel)
            .join(qMcpModel).on(qRobotKnowledgeModel.kbId.eq(qMcpModel.id))
            .where(qRobotKnowledgeModel.robotId.eq(robotId)
                   .and(qRobotKnowledgeModel.answerStrategy.eq(AnswerStrategyEnum.MCP)))
            .fetch();

        // 2. 过滤出有效的MCP
        List<Long> validMcpIds = new ArrayList<>();
        for (Long mcpId : mcpIds) {
            if (robotMcpIds.contains(mcpId)) {
                validMcpIds.add(mcpId);
            } else {
                log.warn("MCP {} 不属于机器人 {}，已跳过", mcpId, robotId);
            }
        }

        // 3. 只查询有效的MCP
        whereBuilder.and(qMcpModel.id.in(validMcpIds));
    }
    // 如果mcpIds为空，查询所有关联的MCP（原有逻辑）
}
```

### 4. 服务层调用

**ChatServiceImpl.java**
- **保持简洁**: 不进行任何特殊判断，所有逻辑由策略实现类处理
- **统一调用**: 使用相同的方法调用所有策略

```java
// 使用对应策略自主收集资源并构建参数
HashMap<String, Object> param = answerStrategyFactory.getAnswerStrategyService(answerStrategy)
        .buildChatParamsWithResourceCollection(chatVO, isSimulate, modelGroupId, queryFactory);
```

## 🎯 实现优势

### ✅ 符合策略模式
- **职责单一**: 每个策略实现类只关注自己的逻辑
- **封装性好**: MCP的特殊处理逻辑完全封装在MCP策略类内部
- **扩展性强**: 其他策略可以类似地处理自己的特殊参数

### ✅ 代码简洁
- **服务层简洁**: ChatServiceImpl不需要特殊判断逻辑
- **接口统一**: 所有策略使用相同的方法签名
- **维护性好**: 逻辑集中，便于维护和测试

### ✅ 权限安全
- **严格验证**: 确保指定的MCP属于当前机器人
- **优雅降级**: 无效的MCP会被跳过，不影响其他有效MCP
- **详细日志**: 便于调试和审计

## 🔄 使用流程

1. **前端传参**: 在`ChatWithRobotReq`中传递`mcpIds`（可选）
2. **数据转换**: `mcpIds`通过`OrikaUtils.convert`传递到`ChatVO`
3. **策略执行**: MCP策略实现类从`ChatVO`获取`mcpIds`并处理
4. **权限验证**: 验证指定的MCP是否属于当前机器人
5. **参数构建**: 只使用验证通过的MCP构建对话参数

## 📝 示例

### 使用所有MCP
```json
{
  "question": "查询天气",
  "robotId": 1001,
  "mcpIds": null  // 或不传递此字段
}
```

### 指定特定MCP
```json
{
  "question": "查询天气",
  "robotId": 1001,
  "mcpIds": [100, 101, 102]  // 只使用这些MCP
}
```

这种实现方式更加符合面向对象设计原则，代码结构更清晰，维护性更好。

## 🎯 使用示例

### 场景1: 使用所有关联的MCP

```json
{
  "question": "查询明天的天气情况",
  "robotId": 1001,
  "sessionId": 1234567890
  // mcpIds为空或null，使用所有关联的MCP
}
```

### 场景2: 指定特定的MCP服务

```json
{
  "question": "查询明天的天气情况", 
  "robotId": 1001,
  "sessionId": 1234567890,
  "mcpIds": [2001, 2002]  // 只使用指定的MCP服务
}
```

## 🔍 验证逻辑

1. **空值处理**: `mcpIds`为空时，使用所有关联的MCP
2. **权限验证**: 验证指定的MCP是否属于当前机器人
3. **安全过滤**: 只使用验证通过的MCP，跳过无权限的MCP
4. **空列表处理**: 如果验证后没有有效的MCP，返回空的dynamic_servers列表

## 📊 日志输出

- **指定MCP**: `指定MCP列表: [2001, 2002]`
- **验证后**: `验证后的有效MCP列表: [2001]`
- **权限警告**: `MCP 2002 不属于机器人 1001，已跳过`
- **结果统计**: `收集到1个MCP服务配置(指定的MCP)`

## ⚠️ 注意事项

1. **向后兼容**: 不影响现有的MCP对话功能
2. **权限安全**: 严格验证MCP的归属权限
3. **错误处理**: 优雅处理无效的MCP ID
4. **性能考虑**: 最小化额外的数据库查询
5. **日志监控**: 详细记录验证和过滤过程

## 🚀 后续扩展

该实现为其他策略的类似需求提供了参考模式：
- 文件策略的文件ID过滤
- 知识库策略的知识库过滤
- 数据库策略的数据源过滤 